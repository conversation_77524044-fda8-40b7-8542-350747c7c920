import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:note_x/lib.dart';

// Basic editor for transcript text
class TranscriptEditWidget extends StatelessWidget {
  const TranscriptEditWidget({
    super.key,
    required this.focusNode,
    required this.quillController,
    required this.scrollController,
    required this.fontSize,
    this.showQuillTool = true,
    this.readOnly = false,
  });

  final FocusNode focusNode;
  final QuillController quillController;
  final ScrollController scrollController;
  final double fontSize;
  final bool showQuillTool;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        showQuillTool && !readOnly
            ? QuillToolbarWrapper(
                controller: quillController,
                focusNode: focusNode,
              )
            : const SizedBox.shrink(),
        !readOnly
            ? QuillEditor(
                focusNode: focusNode,
                controller: quillController,
                scrollController: scrollController,
                configurations: QuillEditorConfigurations(
                  scrollable: false,
                  autoFocus: false,
                  expands: false,
                  showCursor: true,
                  padding: EdgeInsets.zero,
                  keyboardAppearance: Brightness.dark,
                  customStyles: DefaultStyles(
                    paragraph: DefaultTextBlockStyle(
                      TextStyle(
                        fontSize: fontSize,
                        color: context.colorScheme.mainPrimary,
                        height: 1.3,
                      ),
                      HorizontalSpacing.zero,
                      const VerticalSpacing(8, 8),
                      const VerticalSpacing(8, 8),
                      null,
                    ),
                    bold: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: fontSize,
                      color: context.colorScheme.mainPrimary,
                    ),
                  ),
                  minHeight: null,
                ),
              )
            : CommonText(
                quillController.document.toPlainText(),
                style: TextStyle(
                  fontSize: fontSize,
                  color: context.colorScheme.mainPrimary,
                ),
                maxLines: null,
              ),
      ],
    );
  }
}

// A more advanced editor that shows timestamps in non-editable format
class TranscriptItemsEditor extends StatefulWidget {
  const TranscriptItemsEditor({
    super.key,
    required this.items,
    required this.onItemsChanged,
    this.readOnly = false,
    this.fontSize = 16,
    this.initialFocusIndex = -1,
  });

  final List<TranscriptEditItem> items;
  final Function(List<TranscriptEditItem>) onItemsChanged;
  final bool readOnly;
  final double fontSize;
  final int initialFocusIndex;

  @override
  State<TranscriptItemsEditor> createState() => _TranscriptItemsEditorState();
}

class _TranscriptItemsEditorState extends State<TranscriptItemsEditor> {
  late List<TranscriptEditItem> _items;
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;

  // Track the last tapped item index
  int _lastTappedIndex = -1;
  // Track the last tapped position in the text
  int _lastTappedPosition = -1;

  @override
  void initState() {
    super.initState();
    _items = List.from(widget.items);
    _focusNodes = List.generate(_items.length, (index) => FocusNode());

    // Set the last tapped index from initialFocusIndex
    if (widget.initialFocusIndex >= 0 && widget.initialFocusIndex < _items.length) {
      _lastTappedIndex = widget.initialFocusIndex;
      // Set position to the end of the text
      _lastTappedPosition = _items[widget.initialFocusIndex].content.length;
    }

    _initializeControllers();

    // Focus the initial field after a short delay
    if (!widget.readOnly && _lastTappedIndex >= 0 && _lastTappedIndex < _focusNodes.length) {
      Future.delayed(const Duration(milliseconds: 100), () {
        _focusNodes[_lastTappedIndex].requestFocus();

        // Set cursor position to the end of the text
        if (_lastTappedPosition >= 0 && _lastTappedIndex < _controllers.length) {
          _controllers[_lastTappedIndex].selection = TextSelection.fromPosition(
            TextPosition(offset: _lastTappedPosition),
          );
        }
      });
    }
  }

  void _initializeControllers() {
    _controllers = [];

    for (int i = 0; i < _items.length; i++) {
      final item = _items[i];
      final controller = TextEditingController(text: item.content);

      // Add listener for text changes
      controller.addListener(() {
        // Only update if the controller is still in our list
        if (i < _items.length) {
          setState(() {
            _items[i] = TranscriptEditItem(
              timestamp: _items[i].timestamp,
              content: controller.text,
            );
          });
          widget.onItemsChanged(_items);
        }
      });

      _controllers.add(controller);
    }

    // If we have a last tapped index, set the cursor position
    if (_lastTappedIndex >= 0 && _lastTappedIndex < _controllers.length && _lastTappedPosition >= 0) {
      final controller = _controllers[_lastTappedIndex];
      if (_lastTappedPosition <= controller.text.length) {
        controller.selection = TextSelection.fromPosition(
          TextPosition(offset: _lastTappedPosition),
        );
      }
    }
  }

  @override
  void didUpdateWidget(TranscriptItemsEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.items != oldWidget.items) {
      _items = List.from(widget.items);
      _disposeControllers();
      _disposeFocusNodes();
      _focusNodes = List.generate(_items.length, (index) => FocusNode());
      _initializeControllers();
    }

    // Handle initialFocusIndex changes
    if (widget.initialFocusIndex != oldWidget.initialFocusIndex &&
        widget.initialFocusIndex >= 0 &&
        widget.initialFocusIndex < _items.length) {
      _lastTappedIndex = widget.initialFocusIndex;
      _lastTappedPosition = _items[widget.initialFocusIndex].content.length;

      if (!widget.readOnly && _lastTappedIndex < _focusNodes.length) {
        Future.delayed(const Duration(milliseconds: 50), () {
          _focusNodes[_lastTappedIndex].requestFocus();

          if (_lastTappedIndex < _controllers.length) {
            _controllers[_lastTappedIndex].selection = TextSelection.fromPosition(
              TextPosition(offset: _lastTappedPosition),
            );
          }
        });
      }
    }
  }

  void _disposeControllers() {
    for (final controller in _controllers) {
      controller.dispose();
    }
  }

  void _disposeFocusNodes() {
    for (final focusNode in _focusNodes) {
      focusNode.dispose();
    }
  }

  @override
  void dispose() {
    _disposeControllers();
    _disposeFocusNodes();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _items.length,
          itemBuilder: (context, index) {
            final item = _items[index];

            return Padding(
              padding: EdgeInsets.only(bottom: 16.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Non-editable timestamp
                  Padding(
                    padding: EdgeInsets.only(top: 2.h),
                    child: CommonText(
                      item.timestamp,
                      style: TextStyle(
                        fontSize: widget.fontSize - 2,
                        color: context.colorScheme.mainGray,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  // Editable transcript content
                  Expanded(
                    child: widget.readOnly
                        ? CommonText(
                            item.content,
                            style: TextStyle(
                              fontSize: widget.fontSize,
                              color: context.colorScheme.mainPrimary,
                            ),
                          )
                        : TextField(
                            controller: _controllers[index],
                            focusNode: _focusNodes[index],
                            style: TextStyle(
                              fontSize: widget.fontSize,
                              color: context.colorScheme.mainPrimary,
                            ),
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.zero,
                              isDense: true,
                            ),
                            maxLines: null,
                            onTap: () {
                              for (int i = 0; i < _focusNodes.length; i++) {
                                if (i != index && _focusNodes[i].hasFocus) {
                                  _focusNodes[i].unfocus();
                                }
                              }

                              // Save the current index when tapped
                              _lastTappedIndex = index;
                              // We'll get the position from the selection
                              Future.delayed(Duration.zero, () {
                                if (_controllers[index].selection.isValid) {
                                  _lastTappedPosition = _controllers[index].selection.baseOffset;
                                }
                              });
                            },
                          ),
                  ),
                ],
              ),
            );
          },
        ),
        KeyboardVisibilityBuilder(
          builder: (context, isKeyboardVisible) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: isKeyboardVisible ? kToolbarHeight : 0,
            );
          },
        ),
      ],
    );
  }
}

class TranscriptEditItem {
  final String timestamp;
  final String content;

  TranscriptEditItem({
    required this.timestamp,
    required this.content,
  });

  @override
  String toString() {
    return '$timestamp: $content';
  }

  static List<TranscriptEditItem> parseFromText(String text) {
    final List<TranscriptEditItem> items = [];
    final List<String> lines = text.split('\n\n');

    for (final line in lines) {
      final colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        final timestamp = line.substring(0, colonIndex).trim();
        final content = line.substring(colonIndex + 1).trim();
        items.add(TranscriptEditItem(timestamp: timestamp, content: content));
      }
    }

    return items;
  }

  static String formatToText(List<TranscriptEditItem> items) {
    return items.map((item) => '${item.timestamp}: ${item.content}').join('\n\n');
  }
}